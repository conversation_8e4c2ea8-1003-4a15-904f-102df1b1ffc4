import { post_types, post_statuses } from '@/db/schema/posts';
import { post_engagement_types, user_roles } from '@/db/schema';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { RepositoryService } from '@/repositories/repository.service';
import { PostRepository } from '@/repositories/post.repository';
import { User } from '@/db/schema';
import { UploadService } from '@/upload/upload.service';
import { Test } from '@nestjs/testing';
import { PostService } from './post.service';
import { PointSystemRepository } from '@/point-system/repository/point_system.repository';
import { CacheService } from '@app/shared/redis/cache.service';
import { PostNotificationService } from './services/post-notification.service';
import { getQueueToken } from '@nestjs/bullmq';
import { QueueName } from '@app/shared/queue/queue.constants';
import { NotFoundException, UnauthorizedException } from '@nestjs/common';

const mockUser: User = {
  id: '1',
  email: '<EMAIL>',
  role: 'admin',
  state: 'verified',
  profile_pic_url: null,
  deleted_at: null,
  created_at: 'date string',
  updated_at: 'date string',
  deleted: false,
};

const mockStudentAdminUser: User = {
  id: 'student_admin_id',
  email: '<EMAIL>',
  role: user_roles.STUDENT_ADMIN,
  state: 'verified',
  profile_pic_url: null,
  deleted_at: null,
  created_at: 'date string',
  updated_at: 'date string',
  deleted: false,
  student_profile: {
    id: 'student_profile_id',
    club_id: 'club_id',
    first_name: 'John',
    last_name: 'Doe',
  } as any,
};

describe('test PostService', () => {
  let postService: PostService;
  let drizzleService: DrizzleService;
  let postRepository: PostRepository;

  class DrizzleServiceMock {
    db = {
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      returning: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      on: jest.fn().mockReturnThis(),
      count: jest.fn().mockReturnThis(),
      groupBy: jest.fn().mockReturnThis(),
    };
  }

  class PostRepositoryMock {}

  class RepositoryServiceMock {
    createPost = jest.fn();
  }

  class UploadServiceMock {}

  class PointSystemRepositoryMock {} // Create a mock for PointSystemRepository

  class CacheServiceMock {
    generateKey = jest.fn().mockReturnValue('mock-key');
    generateResourceKey = jest.fn().mockReturnValue('mock-resource-key');
    get = jest.fn().mockResolvedValue(null);
    set = jest.fn().mockResolvedValue(true);
    del = jest.fn().mockResolvedValue(true);
    invalidateMany = jest.fn().mockResolvedValue(undefined);
  }

  class PostNotificationServiceMock {
    sendPostNotification = jest.fn().mockResolvedValue(undefined);
  }

  // Mock for BullMQ Queue
  class QueueMock {
    add = jest.fn().mockResolvedValue({ id: 'mock-job-id' });
    process = jest.fn();
    on = jest.fn();
  }

  beforeEach(async () => {
    const DrizzleServiceProvider = {
      provide: DrizzleService,
      useClass: DrizzleServiceMock,
    };
    const PostRepositoryProvider = {
      provide: PostRepository,
      useClass: PostRepositoryMock,
    };
    const RepositoryServiceProvider = {
      provide: RepositoryService,
      useClass: RepositoryServiceMock,
    };
    const UploadServiceProvider = {
      provide: UploadService,
      useClass: UploadServiceMock,
    };
    const PointSystemRepositoryProvider = {
      provide: PointSystemRepository,
      useClass: PointSystemRepositoryMock,
    };
    const CacheServiceProvider = {
      provide: CacheService,
      useClass: CacheServiceMock,
    };
    const PostNotificationServiceProvider = {
      provide: PostNotificationService,
      useClass: PostNotificationServiceMock,
    };

    const QueueProvider = {
      provide: getQueueToken(QueueName.UPLOAD),
      useClass: QueueMock,
    };

    const moduleRef = await Test.createTestingModule({
      imports: [],
      controllers: [],
      providers: [
        PostService,
        DrizzleServiceProvider,
        PostRepositoryProvider,
        RepositoryServiceProvider,
        UploadServiceProvider,
        PointSystemRepositoryProvider,
        CacheServiceProvider,
        PostNotificationServiceProvider,
        QueueProvider,
      ],
    }).compile();

    postService = moduleRef.get<PostService>(PostService);
    drizzleService = moduleRef.get<DrizzleService>(DrizzleService);
    postRepository = moduleRef.get<PostRepository>(PostRepository);
  });

  it('should be defined', () => {
    expect(postService).toBeDefined();
  });

  describe('createClubGeneralPost', () => {
    const postInput = {
      title: 'New Post',
      description: 'A new post',
      post_type: post_types.GENERAL,
      status: post_statuses.DRAFT,
      notify_users: false,
      scheduledAt: null,
    };

    const mockClub = {
      id: 'club_id',
      name: 'Test Club',
      club_admin: 'student_admin_id',
    };

    beforeEach(() => {
      // Reset mocks
      jest.clearAllMocks();
    });

    it('should create a new club post successfully for student admin', async () => {
      // Mock club exists
      jest.spyOn(drizzleService.db, 'select').mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue([mockClub]),
        }),
      } as any);

      const mockCreatedPost = {
        id: 'post_id',
        ...postInput,
        postedBy: mockStudentAdminUser.id,
        club_id: 'club_id',
        created_at: new Date(),
        updated_at: new Date(),
      };

      postRepository.createPost = jest
        .fn()
        .mockResolvedValue([mockCreatedPost]);

      const result = await postService.createClubGeneralPost(
        postInput,
        mockStudentAdminUser,
        'club_id',
      );

      expect(result).toEqual([mockCreatedPost]);
      expect(postRepository.createPost).toHaveBeenCalledWith({
        title: postInput.title,
        description: postInput.description,
        post_type: postInput.post_type,
        status: postInput.status,
        postedBy: mockStudentAdminUser.id,
        club_id: 'club_id',
        notify_users: false,
        scheduledAt: null,
      });
    });

    it('should throw NotFoundException when club does not exist', async () => {
      jest.spyOn(drizzleService.db, 'select').mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue([]),
        }),
      } as any);

      await expect(
        postService.createClubGeneralPost(
          postInput,
          mockStudentAdminUser,
          'non_existent_club_id',
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw UnauthorizedException when user is not club admin', async () => {
      const wrongClub = {
        ...mockClub,
        club_admin: 'different_admin_id',
      };

      jest.spyOn(drizzleService.db, 'select').mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue([wrongClub]),
        }),
      } as any);

      await expect(
        postService.createClubGeneralPost(
          postInput,
          mockStudentAdminUser,
          'club_id',
        ),
      ).rejects.toThrow(UnauthorizedException);
    });

    it('should throw NotFoundException when student has no profile', async () => {
      const userWithoutProfile = {
        ...mockStudentAdminUser,
        student_profile: undefined,
      };

      jest.spyOn(drizzleService.db, 'select').mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue([mockClub]),
        }),
      } as any);

      await expect(
        postService.createClubGeneralPost(
          postInput,
          userWithoutProfile,
          'club_id',
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should handle post with attachments by setting status to PENDING', async () => {
      jest.spyOn(drizzleService.db, 'select').mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue([mockClub]),
        }),
      } as any);

      const mockCreatedPost = {
        id: 'post_id',
        ...postInput,
        status: post_statuses.PENDING,
        postedBy: mockStudentAdminUser.id,
        club_id: 'club_id',
        created_at: new Date(),
        updated_at: new Date(),
      };

      postRepository.createPost = jest
        .fn()
        .mockResolvedValue([mockCreatedPost]);

      const mockAttachments = [
        { originalname: 'test.jpg', buffer: Buffer.from('test') },
      ] as Express.Multer.File[];

      await postService.createClubGeneralPost(
        postInput,
        mockStudentAdminUser,
        'club_id',
        mockAttachments,
      );

      expect(postRepository.createPost).toHaveBeenCalledWith(
        expect.objectContaining({
          status: post_statuses.PENDING,
        }),
      );
    });

    it('should handle non-student admin users', async () => {
      const adminUser = {
        ...mockUser,
        role: 'admin' as const,
      };

      jest.spyOn(drizzleService.db, 'select').mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue([mockClub]),
        }),
      } as any);

      const mockCreatedPost = {
        id: 'post_id',
        ...postInput,
        postedBy: adminUser.id,
        club_id: 'club_id',
        created_at: new Date(),
        updated_at: new Date(),
      };

      postRepository.createPost = jest
        .fn()
        .mockResolvedValue([mockCreatedPost]);

      const result = await postService.createClubGeneralPost(
        postInput,
        adminUser,
        'club_id',
      );

      expect(result).toEqual(mockCreatedPost);
      expect(postRepository.createPost).toHaveBeenCalledWith(
        expect.objectContaining({
          postedBy: adminUser.id,
          club_id: 'club_id',
        }),
      );
    });
  });

  describe('getTrendingById', () => {
    const postId = '123e4567-e89b-12d3-a456-426614174000';
    const mockPost = {
      id: postId,
      title: 'Trending Post',
      description: 'This is a trending post',
      status: 'active',
      type: 'general',
      postedBy: {
        id: 'user-123',
        profile: {
          id: 'profile-123',
          email: '<EMAIL>',
          name: 'Test User',
        },
        student_profile: {
          id: 'student-123',
          first_name: 'Test',
          last_name: 'User',
        },
      },
      countries: [],
      institutions: [],
      opportunity: null,
      event: null,
      postEngagements: [
        {
          id: 'engagement-1',
          post_engagement_type: post_engagement_types.like,
          student_profile_id: 'student-1',
        },
        {
          id: 'engagement-2',
          post_engagement_type: post_engagement_types.like,
          student_profile_id: 'student-2',
        },
        {
          id: 'engagement-3',
          post_engagement_type: post_engagement_types.share,
          student_profile_id: 'student-3',
        },
      ],
      images: [],
      club: null,
    };

    beforeEach(() => {
      // Mock the query method for getTrendingById
      drizzleService.db.query = {
        posts: {
          findFirst: jest.fn(),
        },
      } as any;
    });

    it('should return a trending post with engagement metrics when found', async () => {
      const expectedResult = {
        ...mockPost,
        engagementMetrics: {
          totalEngagements: 3,
          likes: 2,
          shares: 1,
          trendingScore: 4, // 2 likes * 1 + 1 share * 2
        },
      };

      (drizzleService.db.query.posts.findFirst as jest.Mock).mockResolvedValue(
        mockPost,
      );

      const result = await postService.getTrendingById(postId);

      expect(result).toEqual(expectedResult);
      expect(drizzleService.db.query.posts.findFirst).toHaveBeenCalledWith({
        where: expect.anything(),
        with: expect.objectContaining({
          postedBy: expect.any(Object),
          countries: expect.any(Object),
          institutions: expect.any(Object),
          opportunity: true,
          event: true,
          postEngagements: true,
          images: true,
          club: true,
        }),
      });
    });

    it('should return post with zero engagement metrics when no engagements', async () => {
      const postWithNoEngagements = {
        ...mockPost,
        postEngagements: [],
      };

      (drizzleService.db.query.posts.findFirst as jest.Mock).mockResolvedValue(
        postWithNoEngagements,
      );

      const result = await postService.getTrendingById(postId);

      expect(result.engagementMetrics).toEqual({
        totalEngagements: 0,
        likes: 0,
        shares: 0,
        trendingScore: 0,
      });
    });

    it('should throw NotFoundException when post not found', async () => {
      (drizzleService.db.query.posts.findFirst as jest.Mock).mockResolvedValue(
        null,
      );

      await expect(postService.getTrendingById(postId)).rejects.toThrow(
        new NotFoundException(`Trending post with ID ${postId} not found`),
      );
    });

    it('should handle invalid UUID format', async () => {
      const invalidId = 'invalid-uuid';
      (drizzleService.db.query.posts.findFirst as jest.Mock).mockResolvedValue(
        null,
      );

      await expect(postService.getTrendingById(invalidId)).rejects.toThrow(
        new NotFoundException(`Trending post with ID ${invalidId} not found`),
      );
    });
  });

  describe('deletePost', () => {
    const postId = 'test-post-id';
    const mockPost = {
      id: postId,
      title: 'Test Post',
      postedBy: mockUser.id,
      status: post_statuses.ACTIVE,
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should delete post successfully when user is the author', async () => {
      jest.spyOn(drizzleService.db, 'select').mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue([mockPost]),
        }),
      } as any);

      jest.spyOn(drizzleService.db, 'delete').mockReturnValue({
        where: jest.fn().mockResolvedValue(undefined),
      } as any);

      await postService.deletePost(postId, mockUser);

      expect(drizzleService.db.select).toHaveBeenCalled();
      expect(drizzleService.db.delete).toHaveBeenCalled();
    });

    it('should throw NotFoundException when post does not exist', async () => {
      jest.spyOn(drizzleService.db, 'select').mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue([]),
        }),
      } as any);

      await expect(postService.deletePost(postId, mockUser)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw UnauthorizedException when STUDENT_ADMIN user is not the author', async () => {
      const studentAdminUser = {
        ...mockStudentAdminUser,
      };

      const postByAnotherUser = {
        ...mockPost,
        postedBy: 'another-user-id',
      };

      jest.spyOn(drizzleService.db, 'select').mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue([postByAnotherUser]),
        }),
      } as any);

      await expect(
        postService.deletePost(postId, studentAdminUser),
      ).rejects.toThrow(UnauthorizedException);
    });

    it('should allow non-student admin users to delete any post', async () => {
      const adminUser = {
        ...mockUser,
        role: 'admin' as const,
      };

      const postByAnotherUser = {
        ...mockPost,
        postedBy: 'another-user-id',
      };

      jest.spyOn(drizzleService.db, 'select').mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue([postByAnotherUser]),
        }),
      } as any);

      jest.spyOn(drizzleService.db, 'delete').mockReturnValue({
        where: jest.fn().mockResolvedValue(undefined),
      } as any);

      await postService.deletePost(postId, adminUser);

      expect(drizzleService.db.delete).toHaveBeenCalled();
    });
  });
});
